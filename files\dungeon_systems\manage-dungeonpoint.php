<?php 
$user->restrictionUser(true, $conn); 
$params = array();
$options = array("Scrollable" => SQLSRV_CURSOR_KEYSET);

// ตรวจสอบการมีอยู่ของตาราง cabal_DungeonPoint_table
$checkTableQuery = "
    SELECT COUNT(*) as TableExists 
    FROM INFORMATION_SCHEMA.TABLES 
    WHERE TABLE_NAME = 'cabal_DungeonPoint_table'
";
$checkTableResult = sqlsrv_query($conn, $checkTableQuery);
$tableExists = false;
if ($checkTableResult) {
    $tableInfo = sqlsrv_fetch_array($checkTableResult, SQLSRV_FETCH_ASSOC);
    $tableExists = $tableInfo['TableExists'] > 0;
}

// ดึงสถิติข้อมูล Dungeon Point
$totalPlayersQuery = "SELECT COUNT(*) as total FROM [".DATABASE_SV."].[dbo].cabal_DungeonPoint_table";
$totalPlayersResult = sqlsrv_query($conn, $totalPlayersQuery);
$totalPlayers = 0;
if ($totalPlayersResult) {
    $totalPlayersData = sqlsrv_fetch_array($totalPlayersResult, SQLSRV_FETCH_ASSOC);
    $totalPlayers = $totalPlayersData['total'];
}

$totalDPQuery = "SELECT SUM(DP) as totalDP FROM [".DATABASE_SV."].[dbo].cabal_DungeonPoint_table";
$totalDPResult = sqlsrv_query($conn, $totalDPQuery);
$totalDP = 0;
if ($totalDPResult) {
    $totalDPData = sqlsrv_fetch_array($totalDPResult, SQLSRV_FETCH_ASSOC);
    $totalDP = $totalDPData['totalDP'] ?? 0;
}

$avgDPQuery = "SELECT AVG(CAST(DP as FLOAT)) as avgDP FROM [".DATABASE_SV."].[dbo].cabal_DungeonPoint_table WHERE DP > 0";
$avgDPResult = sqlsrv_query($conn, $avgDPQuery);
$avgDP = 0;
if ($avgDPResult) {
    $avgDPData = sqlsrv_fetch_array($avgDPResult, SQLSRV_FETCH_ASSOC);
    $avgDP = round($avgDPData['avgDP'] ?? 0, 2);
}

$activeDPQuery = "SELECT COUNT(*) as activeDP FROM [".DATABASE_SV."].[dbo].cabal_DungeonPoint_table WHERE DP > 0";
$activeDPResult = sqlsrv_query($conn, $activeDPQuery);
$activeDP = 0;
if ($activeDPResult) {
    $activeDPData = sqlsrv_fetch_array($activeDPResult, SQLSRV_FETCH_ASSOC);
    $activeDP = $activeDPData['activeDP'];
}

// จัดการการส่งฟอร์ม
if (isset($_POST['btn_update_dp'])) {
    $characterIdx = intval($_POST['character_idx']);
    $newDP = intval($_POST['new_dp']);
    $action = $_POST['action'];
    
    if ($characterIdx > 0) {
        if ($action === 'set') {
            // ตั้งค่า DP ใหม่
            $updateQuery = "UPDATE [".DATABASE_SV."].[dbo].cabal_DungeonPoint_table SET DP = ?, DPLastGetTime = GETDATE() WHERE CharacterIdx = ?";
            $updateResult = sqlsrv_query($conn, $updateQuery, array($newDP, $characterIdx));
        } elseif ($action === 'add') {
            // เพิ่ม DP
            $updateQuery = "UPDATE [".DATABASE_SV."].[dbo].cabal_DungeonPoint_table SET DP = DP + ?, DPLastGetTime = GETDATE() WHERE CharacterIdx = ?";
            $updateResult = sqlsrv_query($conn, $updateQuery, array($newDP, $characterIdx));
        } elseif ($action === 'subtract') {
            // ลด DP
            $updateQuery = "UPDATE [".DATABASE_SV."].[dbo].cabal_DungeonPoint_table SET DP = CASE WHEN DP - ? < 0 THEN 0 ELSE DP - ? END, DPLastGetTime = GETDATE() WHERE CharacterIdx = ?";
            $updateResult = sqlsrv_query($conn, $updateQuery, array($newDP, $newDP, $characterIdx));
        }
        
        if ($updateResult) {
            echo "<script>setTimeout(function () { Swal.fire('สำเร็จ!','อัปเดต Dungeon Point เรียบร้อยแล้ว','success'); }, 100);</script>";
        } else {
            echo "<script>setTimeout(function () { Swal.fire('ผิดพลาด!','ไม่สามารถอัปเดต Dungeon Point ได้','error'); }, 100);</script>";
        }
    }
}
?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-dungeon"></i> ระบบตรวจสอบ <span class="fw-300">Dungeon Point</span>
    </h1>
    <div class="subheader-block d-lg-flex align-items-center">
        <div class="d-flex mr-4">
            <div class="mr-2">
                <span class="peity-donut"
                    data-peity="{ &quot;fill&quot;: [&quot;#28a745&quot;, &quot;#dc3545&quot;],  &quot;innerRadius&quot;: 14, &quot;radius&quot;: 20 }"
                    style="display: none;"><?php echo $activeDP; ?>/<?php echo $totalPlayers; ?></span>
            </div>
            <div>
                <label class="fs-sm mb-0 mt-2 mt-md-0">ผู้เล่นที่มี DP</label>
                <h4 class="font-weight-bold mb-0"><?php echo number_format($activeDP); ?> / <?php echo number_format($totalPlayers); ?></h4>
            </div>
        </div>
        <div class="d-flex mr-0">
            <div class="mr-2">
                <span class="peity-bar" data-peity="{ &quot;fill&quot;: [&quot;#007bff&quot;] }" style="display: none;">
                    <?php echo min($avgDP/100, 1); ?>
                </span>
            </div>
            <div>
                <label class="fs-sm mb-0 mt-2 mt-md-0">ค่าเฉลี่ย DP</label>
                <h4 class="font-weight-bold mb-0"><?php echo number_format($avgDP, 2); ?> คะแนน</h4>
            </div>
        </div>
    </div>
</div>

<!-- สถิติ Cards -->
<div class="row">
    <div class="col-sm-6 col-xl-3">
        <div class="p-3 bg-primary-300 rounded overflow-hidden position-relative text-white mb-g">
            <div class="">
                <h3 class="display-4 d-block l-h-n m-0 fw-500">
                    <?php echo number_format($totalPlayers); ?>
                    <small class="m-0 l-h-n">ผู้เล่นทั้งหมด</small>
                </h3>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-xl-3">
        <div class="p-3 bg-success-400 rounded overflow-hidden position-relative text-white mb-g">
            <div class="">
                <h3 class="display-4 d-block l-h-n m-0 fw-500">
                    <?php echo number_format($activeDP); ?>
                    <small class="m-0 l-h-n">มี DP มากกว่า 0</small>
                </h3>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-xl-3">
        <div class="p-3 bg-warning-400 rounded overflow-hidden position-relative text-white mb-g">
            <div class="">
                <h3 class="display-4 d-block l-h-n m-0 fw-500">
                    <?php echo number_format($totalDP); ?>
                    <small class="m-0 l-h-n">DP รวมทั้งหมด</small>
                </h3>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-xl-3">
        <div class="p-3 bg-info-200 rounded overflow-hidden position-relative text-white mb-g">
            <div class="">
                <h3 class="display-4 d-block l-h-n m-0 fw-500">
                    <?php echo number_format($avgDP, 2); ?>
                    <small class="m-0 l-h-n">ค่าเฉลี่ย DP</small>
                </h3>
            </div>
        </div>
    </div>
</div>

<?php if (!$tableExists): ?>
<div class="alert alert-warning" role="alert">
    <h4 class="alert-heading">ไม่พบตาราง cabal_DungeonPoint_table!</h4>
    <p>ระบบไม่พบตาราง cabal_DungeonPoint_table ในฐานข้อมูล กรุณาสร้างตารางก่อนใช้งาน</p>
    <hr>
    <p class="mb-0">SQL สำหรับสร้างตาราง:</p>
    <pre class="mt-2">
CREATE TABLE [".DATABASE_SV."].[dbo].[cabal_DungeonPoint_table] (
    [CharacterIdx] int NOT NULL,
    [DP] int DEFAULT 0 NOT NULL,
    [DPLastGetTime] int DEFAULT 0 NOT NULL,
    PRIMARY KEY ([CharacterIdx])
);
    </pre>
</div>
<?php else: ?>

<!-- ตาราง Dungeon Point -->
<div class="row">
    <div class="col-xl-12">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>
                    Manager Dungeon Point <span class="fw-300"><i>ตรวจสอบและจัดการ Dungeon Point</i></span>
                </h2>
                <div class="panel-toolbar">
                    <button class="btn btn-panel" data-action="panel-collapse" data-toggle="tooltip" data-offset="0,10" data-original-title="Collapse"></button>
                    <button class="btn btn-panel" data-action="panel-fullscreen" data-toggle="tooltip" data-offset="0,10" data-original-title="Fullscreen"></button>
                </div>
            </div>
            <div class="panel-container show">
                <div class="panel-content">
                    <!-- ฟิลเตอร์ -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="filter_character">ค้นหาตัวละคร:</label>
                            <input type="text" id="filter_character" class="form-control" placeholder="ชื่อตัวละคร หรือ CharacterIdx">
                        </div>
                        <div class="col-md-4">
                            <label for="filter_dp_min">DP ต่ำสุด:</label>
                            <input type="number" id="filter_dp_min" class="form-control" placeholder="0" min="0">
                        </div>
                        <div class="col-md-4">
                            <label for="filter_dp_max">DP สูงสุด:</label>
                            <input type="number" id="filter_dp_max" class="form-control" placeholder="999999" min="0">
                        </div>
                    </div>

                    <!-- datatable start -->
                    <div class="table-responsive-lg">
                        <table id="dt-dungeonpoint" class="table table-sm table-bordered w-100">
                            <thead>
                                <tr>
                                    <th>UserNum</th>
                                    <th>UserID</th>
                                    <th>CharacterIdx</th>
                                    <th>ชื่อตัวละคร</th>
                                    <th>Dungeon Point</th>
                                    <th>วันที่ได้ DP ล่าสุด</th>
                                    <th>สถานะ</th>
                                    <th>จัดการ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                // generic function to get page
                                function getPage($stmt, $pageNum, $rowsPerPage) {
                                    $offset = ($pageNum - 1) * $rowsPerPage;
                                    $rows = array();
                                    $i = 0;
                                    while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                                        array_push($rows, $row);
                                        $i++;
                                    }
                                    return $rows;
                                }

                                // Set the number of rows to be returned on a page.
                                $rowsPerPage = 100;

                                // Define and execute the query.
                                $sql = "SELECT dp.CharacterIdx, dp.DP, dp.DPLastGetTime, 
                                               ct.Name, ct.Level, ct.Class,
                                               auth.UserNum, auth.ID
                                        FROM [".DATABASE_SV."].[dbo].cabal_DungeonPoint_table dp
                                        LEFT JOIN [".DATABASE_SV."].[dbo].cabal_character_table ct ON dp.CharacterIdx = ct.CharacterIdx
                                        LEFT JOIN [".DATABASE_ACC."].[dbo].cabal_auth_table auth ON ct.UserNum = auth.UserNum
                                        ORDER BY dp.DP DESC, dp.DPLastGetTime DESC";

                                $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                                if (!$stmt) {
                                    die(print_r(sqlsrv_errors(), true));
                                }

                                // Get the total number of rows returned by the query.
                                $rowsReturned = sqlsrv_num_rows($stmt);
                                if ($rowsReturned === false) {
                                    die(print_r(sqlsrv_errors(), true));
                                } elseif ($rowsReturned == 0) {
                                    echo "<tr><td colspan='8' class='text-center'>ไม่พบข้อมูล Dungeon Point</td></tr>";
                                } else {
                                    /* Calculate number of pages. */
                                    $numOfPages = ceil($rowsReturned / $rowsPerPage);
                                    
                                    // Display the selected page of data.
                                    $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                                    $page = getPage($stmt, $pageNum, $rowsPerPage);

                                    foreach ($page as $row) {
                                        $characterIdx = $row[0];
                                        $dp = $row[1];
                                        $dpLastGetTime = $row[2];
                                        $characterName = $userLogin->thaitrans($row[3] ?? 'N/A');
                                        $level = $row[4] ?? 0;
                                        $class = $row[5] ?? 0;
                                        $userNum = $row[6] ?? 0;
                                        $userID = $row[7] ?? 'N/A';
                                        
                                        // แปลงเวลา
                                        $lastGetTimeFormatted = 'ไม่เคยได้';
                                        if ($dpLastGetTime && $dpLastGetTime > 0) {
                                            $lastGetTimeFormatted = date('d/m/Y H:i:s', $dpLastGetTime);
                                        }
                                        
                                        // สถานะ
                                        $status = $dp > 0 ? '<span class="badge badge-success">มี DP</span>' : '<span class="badge badge-secondary">ไม่มี DP</span>';
                                ?>
                                <tr>
                                    <td><?php echo $userNum; ?></td>
                                    <td><?php echo htmlspecialchars($userID); ?></td>
                                    <td><?php echo $characterIdx; ?></td>
                                    <td>
                                        <?php echo htmlspecialchars($characterName); ?>
                                        <small class="text-muted d-block">Lv.<?php echo $level; ?> | Class: <?php echo $class; ?></small>
                                    </td>
                                    <td><span class="badge badge-primary"><?php echo number_format($dp); ?></span></td>
                                    <td><?php echo $lastGetTimeFormatted; ?></td>
                                    <td><?php echo $status; ?></td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#editDPModal" 
                                                data-character-idx="<?php echo $characterIdx; ?>" 
                                                data-character-name="<?php echo htmlspecialchars($characterName); ?>"
                                                data-current-dp="<?php echo $dp; ?>">
                                            <i class="fal fa-edit"></i> แก้ไข
                                        </button>
                                    </td>
                                </tr>
                                <?php 
                                    }
                                }
                                ?>
                            </tbody>
                        </table>
                        <!-- datatable end -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal สำหรับแก้ไข DP -->
<div class="modal fade" id="editDPModal" tabindex="-1" role="dialog" aria-labelledby="editDPModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editDPModalLabel">แก้ไข Dungeon Point</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="post">
                <div class="modal-body">
                    <div class="form-group">
                        <label>ตัวละคร:</label>
                        <input type="text" class="form-control" id="modal_character_name" readonly>
                        <input type="hidden" name="character_idx" id="modal_character_idx">
                    </div>
                    <div class="form-group">
                        <label>DP ปัจจุบัน:</label>
                        <input type="text" class="form-control" id="modal_current_dp" readonly>
                    </div>
                    <div class="form-group">
                        <label for="action">การดำเนินการ:</label>
                        <select name="action" id="action" class="form-control" required>
                            <option value="">เลือกการดำเนินการ</option>
                            <option value="set">ตั้งค่า DP ใหม่</option>
                            <option value="add">เพิ่ม DP</option>
                            <option value="subtract">ลด DP</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="new_dp">จำนวน DP:</label>
                        <input type="number" name="new_dp" id="new_dp" class="form-control" min="0" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">ยกเลิก</button>
                    <button type="submit" name="btn_update_dp" class="btn btn-primary">บันทึก</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // DataTable initialization
    $('#dt-dungeonpoint').DataTable({
        "pageLength": 50,
        "order": [[ 4, "desc" ]],
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Thai.json"
        }
    });
    
    // Modal event handler
    $('#editDPModal').on('show.bs.modal', function (event) {
        var button = $(event.relatedTarget);
        var characterIdx = button.data('character-idx');
        var characterName = button.data('character-name');
        var currentDP = button.data('current-dp');
        
        var modal = $(this);
        modal.find('#modal_character_idx').val(characterIdx);
        modal.find('#modal_character_name').val(characterName);
        modal.find('#modal_current_dp').val(currentDP);
    });
});
</script>

<?php endif; ?>
