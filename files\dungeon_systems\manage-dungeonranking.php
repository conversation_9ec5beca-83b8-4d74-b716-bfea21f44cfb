<?php 
$user->restrictionUser(true, $conn); 
$params = array();
$options = array("Scrollable" => SQLSRV_CURSOR_KEYSET);

// ดึงสถิติข้อมูล Dungeon Ranking
$totalRankingsQuery = "SELECT COUNT(*) as total FROM [".DATABASE_SV."].[dbo].cabal_DungeonRankingParty_table";
$totalRankingsResult = sqlsrv_query($conn, $totalRankingsQuery);
$totalRankings = 0;
if ($totalRankingsResult) {
    $totalRankingsData = sqlsrv_fetch_array($totalRankingsResult, SQLSRV_FETCH_ASSOC);
    $totalRankings = $totalRankingsData['total'];
}

// จำนวน Dungeon ที่แตกต่างกัน
$uniqueDungeonsQuery = "SELECT COUNT(DISTINCT DungeonIdx) as uniqueDungeons FROM [".DATABASE_SV."].[dbo].cabal_DungeonRankingParty_table";
$uniqueDungeonsResult = sqlsrv_query($conn, $uniqueDungeonsQuery);
$uniqueDungeons = 0;
if ($uniqueDungeonsResult) {
    $uniqueDungeonsData = sqlsrv_fetch_array($uniqueDungeonsResult, SQLSRV_FETCH_ASSOC);
    $uniqueDungeons = $uniqueDungeonsData['uniqueDungeons'];
}

// เวลาเคลียร์เร็วที่สุด
$fastestClearQuery = "SELECT MIN(ClearTimeSec) as fastestTime FROM [".DATABASE_SV."].[dbo].cabal_DungeonRankingParty_table";
$fastestClearResult = sqlsrv_query($conn, $fastestClearQuery);
$fastestTime = 0;
if ($fastestClearResult) {
    $fastestTimeData = sqlsrv_fetch_array($fastestClearResult, SQLSRV_FETCH_ASSOC);
    $fastestTime = $fastestTimeData['fastestTime'] ?? 0;
}

// เวลาเคลียร์เฉลี่ย
$avgClearQuery = "SELECT AVG(CAST(ClearTimeSec as FLOAT)) as avgTime FROM [".DATABASE_SV."].[dbo].cabal_DungeonRankingParty_table";
$avgClearResult = sqlsrv_query($conn, $avgClearQuery);
$avgTime = 0;
if ($avgClearResult) {
    $avgTimeData = sqlsrv_fetch_array($avgClearResult, SQLSRV_FETCH_ASSOC);
    $avgTime = round($avgTimeData['avgTime'] ?? 0, 2);
}

// ฟังก์ชันแปลงเวลาเป็นรูปแบบที่อ่านง่าย
function formatTime($seconds) {
    // แปลงเป็น int เพื่อป้องกัน float precision warning
    $totalSeconds = (int)round($seconds);

    $hours = floor($totalSeconds / 3600);
    $minutes = floor(($totalSeconds % 3600) / 60);
    $secs = $totalSeconds % 60;

    if ($hours > 0) {
        return sprintf("%02d:%02d:%02d", $hours, $minutes, $secs);
    } else {
        return sprintf("%02d:%02d", $minutes, $secs);
    }
}
?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-trophy"></i> ระบบตรวจสอบ <span class="fw-300">Dungeon Ranking Party</span>
    </h1>
    <div class="subheader-block d-lg-flex align-items-center">
        <div class="d-flex mr-4">
            <div class="mr-2">
                <span class="peity-donut"
                    data-peity="{ &quot;fill&quot;: [&quot;#ffc107&quot;, &quot;#6c757d&quot;],  &quot;innerRadius&quot;: 14, &quot;radius&quot;: 20 }"
                    style="display: none;"><?php echo $uniqueDungeons; ?>/10</span>
            </div>
            <div>
                <label class="fs-sm mb-0 mt-2 mt-md-0">Dungeon ที่มีการแข่งขัน</label>
                <h4 class="font-weight-bold mb-0"><?php echo number_format($uniqueDungeons); ?> Dungeons</h4>
            </div>
        </div>
        <div class="d-flex mr-0">
            <div class="mr-2">
                <span class="peity-bar" data-peity="{ &quot;fill&quot;: [&quot;#28a745&quot;] }" style="display: none;">
                    <?php echo min($fastestTime/3600, 1); ?>
                </span>
            </div>
            <div>
                <label class="fs-sm mb-0 mt-2 mt-md-0">เวลาเคลียร์เร็วสุด</label>
                <h4 class="font-weight-bold mb-0"><?php echo formatTime($fastestTime); ?></h4>
            </div>
        </div>
    </div>
</div>

<!-- สถิติ Cards -->
<div class="row">
    <div class="col-sm-6 col-xl-3">
        <div class="p-3 bg-primary-300 rounded overflow-hidden position-relative text-white mb-g">
            <div class="">
                <h3 class="display-4 d-block l-h-n m-0 fw-500">
                    <?php echo number_format($totalRankings); ?>
                    <small class="m-0 l-h-n">รายการแข่งขันทั้งหมด</small>
                </h3>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-xl-3">
        <div class="p-3 bg-warning-400 rounded overflow-hidden position-relative text-white mb-g">
            <div class="">
                <h3 class="display-4 d-block l-h-n m-0 fw-500">
                    <?php echo number_format($uniqueDungeons); ?>
                    <small class="m-0 l-h-n">Dungeon ที่แตกต่าง</small>
                </h3>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-xl-3">
        <div class="p-3 bg-success-200 rounded overflow-hidden position-relative text-white mb-g">
            <div class="">
                <h3 class="display-4 d-block l-h-n m-0 fw-500">
                    <?php echo formatTime($fastestTime); ?>
                    <small class="m-0 l-h-n">เวลาเร็วที่สุด</small>
                </h3>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-xl-3">
        <div class="p-3 bg-info-200 rounded overflow-hidden position-relative text-white mb-g">
            <div class="">
                <h3 class="display-4 d-block l-h-n m-0 fw-500">
                    <?php echo formatTime($avgTime); ?>
                    <small class="m-0 l-h-n">เวลาเฉลี่ย</small>
                </h3>
            </div>
        </div>
    </div>
</div>

<!-- ตาราง Dungeon Ranking -->
<div class="row">
    <div class="col-xl-12">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>
                    Dungeon Ranking Party <span class="fw-300"><i>ตรวจสอบการจัดอันดับ Dungeon</i></span>
                </h2>
                <div class="panel-toolbar">
                    <button class="btn btn-panel" data-action="panel-collapse" data-toggle="tooltip" data-offset="0,10" data-original-title="Collapse"></button>
                    <button class="btn btn-panel" data-action="panel-fullscreen" data-toggle="tooltip" data-offset="0,10" data-original-title="Fullscreen"></button>
                </div>
            </div>
            <div class="panel-container show">
                <div class="panel-content">
                    <!-- ฟิลเตอร์ -->
                    <div class="alert alert-info" role="alert">
                        <i class="fal fa-info-circle"></i> <strong>หมายเหตุ:</strong> แสดงข้อมูล 500 อันดับแรก (เรียงตาม Current Rank) เพื่อประสิทธิภาพในการโหลด
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <label for="filter_dungeon">Dungeon ID:</label>
                            <input type="number" id="filter_dungeon" class="form-control" placeholder="DungeonIdx" min="0">
                        </div>
                        <div class="col-md-3">
                            <label for="filter_leader">ชื่อหัวหน้าทีม:</label>
                            <input type="text" id="filter_leader" class="form-control" placeholder="LeaderName">
                        </div>
                        <div class="col-md-3">
                            <label for="filter_rank_min">อันดับต่ำสุด:</label>
                            <input type="number" id="filter_rank_min" class="form-control" placeholder="1" min="1">
                        </div>
                        <div class="col-md-3">
                            <label for="filter_rank_max">อันดับสูงสุด:</label>
                            <input type="number" id="filter_rank_max" class="form-control" placeholder="100" min="1">
                        </div>
                    </div>

                    <!-- datatable start -->
                    <div class="table-responsive-lg">
                        <table id="dt-dungeonranking" class="table table-sm table-bordered w-100">
                            <thead>
                                <tr>
                                    <th>Ranking ID</th>
                                    <th>Dungeon ID</th>
                                    <th>อันดับปัจจุบัน</th>
                                    <th>อันดับก่อนหน้า</th>
                                    <th>หัวหน้าทีม</th>
                                    <th>เวลาเคลียร์</th>
                                    <th>Level รวม</th>
                                    <th>วันที่เคลียร์</th>
                                    <th>รายละเอียด</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                // Define and execute the query (จำกัดที่ 500 แถวเพื่อ performance)
                                $sql = "SELECT TOP 500 
                                               RankingIdx, DungeonIdx, CurrentRank, LastRank,
                                               LeaderCharIdx, LeaderName, ClearTimeSec, LevelSum,
                                               ClearDate, PartyMemberData
                                        FROM [".DATABASE_SV."].[dbo].cabal_DungeonRankingParty_table
                                        ORDER BY CurrentRank ASC, ClearTimeSec ASC";

                                $stmt = sqlsrv_query($conn, $sql);
                                if (!$stmt) {
                                    die(print_r(sqlsrv_errors(), true));
                                }

                                // ตรวจสอบว่ามีข้อมูลหรือไม่
                                $hasData = false;
                                while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                                    $hasData = true;
                                    $rankingIdx = $row['RankingIdx'];
                                    $dungeonIdx = $row['DungeonIdx'];
                                    $currentRank = $row['CurrentRank'];
                                    $lastRank = $row['LastRank'];
                                    $leaderCharIdx = $row['LeaderCharIdx'];
                                    $leaderName = $userLogin->thaitrans($row['LeaderName']);
                                    $clearTimeSec = $row['ClearTimeSec'];
                                    $levelSum = $row['LevelSum'];
                                    $clearDate = $row['ClearDate'];
                                    
                                    // แปลงวันที่
                                    $clearDateFormatted = 'ไม่ระบุ';
                                    if ($clearDate) {
                                        $clearDateFormatted = $clearDate->format('d/m/Y H:i:s');
                                    }
                                    
                                    // สถานะการเปลี่ยนแปลงอันดับ
                                    $rankChange = '';
                                    if ($currentRank < $lastRank) {
                                        $rankChange = '<span class="badge badge-success"><i class="fal fa-arrow-up"></i> ขึ้น</span>';
                                    } elseif ($currentRank > $lastRank) {
                                        $rankChange = '<span class="badge badge-danger"><i class="fal fa-arrow-down"></i> ลง</span>';
                                    } else {
                                        $rankChange = '<span class="badge badge-secondary"><i class="fal fa-minus"></i> เท่าเดิม</span>';
                                    }
                                ?>
                                <tr>
                                    <td><?php echo $rankingIdx; ?></td>
                                    <td><span class="badge badge-primary"><?php echo $dungeonIdx; ?></span></td>
                                    <td>
                                        <span class="badge badge-warning">#<?php echo $currentRank; ?></span>
                                        <?php echo $rankChange; ?>
                                    </td>
                                    <td><span class="badge badge-secondary">#<?php echo $lastRank; ?></span></td>
                                    <td>
                                        <?php echo htmlspecialchars($leaderName); ?>
                                        <small class="text-muted d-block">CharIdx: <?php echo $leaderCharIdx; ?></small>
                                    </td>
                                    <td><span class="badge badge-info"><?php echo formatTime($clearTimeSec); ?></span></td>
                                    <td><?php echo number_format($levelSum); ?></td>
                                    <td><?php echo $clearDateFormatted; ?></td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-info" onclick="viewRankingDetail(<?php echo $rankingIdx; ?>)">
                                            <i class="fal fa-eye"></i> ดูรายละเอียด
                                        </button>
                                    </td>
                                </tr>
                                <?php 
                                }
                                
                                if (!$hasData) {
                                    echo "<tr><td colspan='9' class='text-center'>ไม่พบข้อมูล Dungeon Ranking</td></tr>";
                                }
                                ?>
                            </tbody>
                        </table>
                        <!-- datatable end -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // DataTable initialization
    var table = $('#dt-dungeonranking').DataTable({
        "pageLength": 50,
        "order": [[ 2, "asc" ]],
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Thai.json"
        },
        "processing": true,
        "serverSide": false,
        "responsive": true
    });
    
    // ฟิลเตอร์แบบ Custom
    $('#filter_dungeon').on('keyup', function() {
        table.column(1).search(this.value).draw();
    });
    
    $('#filter_leader').on('keyup', function() {
        table.column(4).search(this.value).draw();
    });
    
    // Custom filter function for rank range
    $.fn.dataTable.ext.search.push(
        function(settings, data, dataIndex) {
            var min = parseInt($('#filter_rank_min').val()) || 1;
            var max = parseInt($('#filter_rank_max').val()) || 999999;
            var currentRank = parseInt(data[2].replace(/[^0-9]/g, '')) || 0;
            
            if (min === 1 && max === 999999) {
                return true; // No filter applied
            }
            
            return currentRank >= min && currentRank <= max;
        }
    );
    
    $('#filter_rank_min, #filter_rank_max').on('keyup', function() {
        table.draw();
    });
    
    // ฟังก์ชันดูรายละเอียด Ranking
    window.viewRankingDetail = function(rankingIdx) {
        // สามารถเพิ่มการแสดง modal หรือเปิดหน้าใหม่เพื่อดูรายละเอียดได้
        alert('ดูรายละเอียด Ranking ID: ' + rankingIdx + '\n(สามารถพัฒนาต่อเพื่อแสดงข้อมูล PartyMemberData)');
    };
});
</script>
