<?php
require('../../_app/dbinfo.inc.php');
require('../../_app/general_config.inc.php');

echo "<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>ทดสอบตาราง Dungeon Ranking Party</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        .test-section { 
            border: 1px solid #ddd; 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px; 
        }
        table { border-collapse: collapse; width: 100%; margin-top: 10px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        pre { background-color: #f5f5f5; padding: 10px; border-radius: 3px; }
    </style>
</head>
<body>";

echo "<h1>🏆 ทดสอบระบบ Dungeon Ranking Party</h1>";

// ฟังก์ชันแปลงเวลา
function formatTime($seconds) {
    $hours = floor($seconds / 3600);
    $minutes = floor(($seconds % 3600) / 60);
    $secs = $seconds % 60;
    
    if ($hours > 0) {
        return sprintf("%02d:%02d:%02d", $hours, $minutes, $secs);
    } else {
        return sprintf("%02d:%02d", $minutes, $secs);
    }
}

// ทดสอบการเชื่อมต่อฐานข้อมูล
echo "<div class='test-section'>
        <h3>🔌 ทดสอบการเชื่อมต่อฐานข้อมูล</h3>";

$conn = db_connect();

if (isset($conn) && $conn !== false) {
    echo "<p class='success'>✅ เชื่อมต่อ Database สำเร็จ</p>";
    
    // ทดสอบ Query พื้นฐาน
    $testQuery = "SELECT @@VERSION AS SQLVersion, DB_NAME() AS DatabaseName";
    $testResult = sqlsrv_query($conn, $testQuery);
    
    if ($testResult) {
        $dbInfo = sqlsrv_fetch_array($testResult, SQLSRV_FETCH_ASSOC);
        echo "<p class='info'>📊 Database: " . htmlspecialchars($dbInfo['DatabaseName']) . "</p>";
        echo "<p class='info'>🔧 SQL Server Version: " . htmlspecialchars($dbInfo['SQLVersion']) . "</p>";
    }
} else {
    echo "<p class='error'>❌ ไม่สามารถเชื่อมต่อ Database ได้</p>";
    if (sqlsrv_errors()) {
        echo "<pre class='error'>" . print_r(sqlsrv_errors(), true) . "</pre>";
    }
}

echo "</div>";

// ทดสอบการมีอยู่ของตาราง cabal_DungeonRankingParty_table
echo "<div class='test-section'>
        <h3>📋 ตรวจสอบตาราง cabal_DungeonRankingParty_table</h3>";

$checkTableQuery = "
    SELECT COUNT(*) as TableExists 
    FROM INFORMATION_SCHEMA.TABLES 
    WHERE TABLE_NAME = 'cabal_DungeonRankingParty_table'
";

$checkTableResult = sqlsrv_query($conn, $checkTableQuery);

if ($checkTableResult) {
    $tableInfo = sqlsrv_fetch_array($checkTableResult, SQLSRV_FETCH_ASSOC);
    
    if ($tableInfo['TableExists'] > 0) {
        echo "<p class='success'>✅ ตาราง cabal_DungeonRankingParty_table มีอยู่ในระบบ</p>";
        
        // ตรวจสอบโครงสร้างตาราง
        $structureQuery = "
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'cabal_DungeonRankingParty_table'
            ORDER BY ORDINAL_POSITION
        ";
        
        $structureResult = sqlsrv_query($conn, $structureQuery);
        
        if ($structureResult) {
            echo "<h4>📊 โครงสร้างตาราง:</h4>";
            echo "<table>";
            echo "<tr><th>Column Name</th><th>Data Type</th><th>Nullable</th><th>Default</th></tr>";
            
            while ($column = sqlsrv_fetch_array($structureResult, SQLSRV_FETCH_ASSOC)) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($column['COLUMN_NAME']) . "</td>";
                echo "<td>" . htmlspecialchars($column['DATA_TYPE']) . "</td>";
                echo "<td>" . htmlspecialchars($column['IS_NULLABLE']) . "</td>";
                echo "<td>" . htmlspecialchars($column['COLUMN_DEFAULT'] ?? 'NULL') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // ตรวจสอบข้อมูลในตาราง
        echo "<h4>📈 สถิติข้อมูล:</h4>";
        
        // จำนวนแถวทั้งหมด
        $countQuery = "SELECT COUNT(*) as total_rows FROM [".DATABASE_SV."].[dbo].cabal_DungeonRankingParty_table";
        $countResult = sqlsrv_query($conn, $countQuery);
        if ($countResult) {
            $countData = sqlsrv_fetch_array($countResult, SQLSRV_FETCH_ASSOC);
            echo "<p class='info'>📊 จำนวนแถวทั้งหมด: " . number_format($countData['total_rows']) . " แถว</p>";
        }
        
        // จำนวน Dungeon ที่แตกต่างกัน
        $uniqueDungeonsQuery = "SELECT COUNT(DISTINCT DungeonIdx) as unique_dungeons FROM [".DATABASE_SV."].[dbo].cabal_DungeonRankingParty_table";
        $uniqueDungeonsResult = sqlsrv_query($conn, $uniqueDungeonsQuery);
        if ($uniqueDungeonsResult) {
            $uniqueDungeonsData = sqlsrv_fetch_array($uniqueDungeonsResult, SQLSRV_FETCH_ASSOC);
            echo "<p class='success'>✅ จำนวน Dungeon ที่แตกต่าง: " . number_format($uniqueDungeonsData['unique_dungeons']) . " Dungeons</p>";
        }
        
        // เวลาเคลียร์เร็วที่สุด
        $fastestQuery = "SELECT MIN(ClearTimeSec) as fastest_time FROM [".DATABASE_SV."].[dbo].cabal_DungeonRankingParty_table";
        $fastestResult = sqlsrv_query($conn, $fastestQuery);
        if ($fastestResult) {
            $fastestData = sqlsrv_fetch_array($fastestResult, SQLSRV_FETCH_ASSOC);
            $fastestTime = $fastestData['fastest_time'] ?? 0;
            echo "<p class='info'>⚡ เวลาเคลียร์เร็วที่สุด: " . formatTime($fastestTime) . " (" . number_format($fastestTime) . " วินาที)</p>";
        }
        
        // เวลาเคลียร์เฉลี่ย
        $avgQuery = "SELECT AVG(CAST(ClearTimeSec as FLOAT)) as avg_time FROM [".DATABASE_SV."].[dbo].cabal_DungeonRankingParty_table";
        $avgResult = sqlsrv_query($conn, $avgQuery);
        if ($avgResult) {
            $avgData = sqlsrv_fetch_array($avgResult, SQLSRV_FETCH_ASSOC);
            $avgTime = round($avgData['avg_time'] ?? 0, 2);
            echo "<p class='info'>📊 เวลาเคลียร์เฉลี่ย: " . formatTime($avgTime) . " (" . number_format($avgTime, 2) . " วินาที)</p>";
        }
        
        // Level รวมสูงสุด
        $maxLevelQuery = "SELECT MAX(LevelSum) as max_level FROM [".DATABASE_SV."].[dbo].cabal_DungeonRankingParty_table";
        $maxLevelResult = sqlsrv_query($conn, $maxLevelQuery);
        if ($maxLevelResult) {
            $maxLevelData = sqlsrv_fetch_array($maxLevelResult, SQLSRV_FETCH_ASSOC);
            echo "<p class='warning'>🏆 Level รวมสูงสุด: " . number_format($maxLevelData['max_level'] ?? 0) . "</p>";
        }
        
        // แสดงข้อมูลตัวอย่าง 5 แถวแรก
        echo "<h4>📋 ข้อมูลตัวอย่าง (5 อันดับแรก):</h4>";
        $sampleQuery = "SELECT TOP 5 * FROM [".DATABASE_SV."].[dbo].cabal_DungeonRankingParty_table ORDER BY CurrentRank ASC";
        $sampleResult = sqlsrv_query($conn, $sampleQuery);
        
        if ($sampleResult) {
            echo "<table>";
            echo "<tr><th>RankingIdx</th><th>DungeonIdx</th><th>CurrentRank</th><th>LeaderName</th><th>ClearTime</th><th>LevelSum</th><th>ClearDate</th></tr>";
            
            while ($sample = sqlsrv_fetch_array($sampleResult, SQLSRV_FETCH_ASSOC)) {
                $clearDateFormatted = 'ไม่ระบุ';
                if ($sample['ClearDate']) {
                    $clearDateFormatted = $sample['ClearDate']->format('d/m/Y H:i:s');
                }
                
                echo "<tr>";
                echo "<td>" . htmlspecialchars($sample['RankingIdx']) . "</td>";
                echo "<td>" . htmlspecialchars($sample['DungeonIdx']) . "</td>";
                echo "<td>#" . htmlspecialchars($sample['CurrentRank']) . "</td>";
                echo "<td>" . htmlspecialchars($sample['LeaderName']) . "</td>";
                echo "<td>" . formatTime($sample['ClearTimeSec']) . "</td>";
                echo "<td>" . number_format($sample['LevelSum']) . "</td>";
                echo "<td>" . htmlspecialchars($clearDateFormatted) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // แสดงข้อมูลแยกตาม Dungeon
        echo "<h4>📊 สถิติแยกตาม Dungeon:</h4>";
        $dungeonStatsQuery = "
            SELECT 
                DungeonIdx,
                COUNT(*) as total_records,
                MIN(ClearTimeSec) as fastest_time,
                AVG(CAST(ClearTimeSec as FLOAT)) as avg_time,
                MAX(LevelSum) as max_level_sum
            FROM [".DATABASE_SV."].[dbo].cabal_DungeonRankingParty_table
            GROUP BY DungeonIdx
            ORDER BY DungeonIdx
        ";
        
        $dungeonStatsResult = sqlsrv_query($conn, $dungeonStatsQuery);
        
        if ($dungeonStatsResult) {
            echo "<table>";
            echo "<tr><th>Dungeon ID</th><th>จำนวนรายการ</th><th>เวลาเร็วสุด</th><th>เวลาเฉลี่ย</th><th>Level รวมสูงสุด</th></tr>";
            
            while ($dungeonStat = sqlsrv_fetch_array($dungeonStatsResult, SQLSRV_FETCH_ASSOC)) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($dungeonStat['DungeonIdx']) . "</td>";
                echo "<td>" . number_format($dungeonStat['total_records']) . "</td>";
                echo "<td>" . formatTime($dungeonStat['fastest_time']) . "</td>";
                echo "<td>" . formatTime(round($dungeonStat['avg_time'], 0)) . "</td>";
                echo "<td>" . number_format($dungeonStat['max_level_sum']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } else {
        echo "<p class='error'>❌ ไม่พบตาราง cabal_DungeonRankingParty_table</p>";
        echo "<p class='warning'>⚠️ กรุณาสร้างตารางด้วย SQL ต่อไปนี้:</p>";
        echo "<pre>
CREATE TABLE [".DATABASE_SV."].[dbo].[cabal_DungeonRankingParty_table] (
    [RankingIdx] int IDENTITY(1,1) NOT NULL,
    [DungeonIdx] int NOT NULL,
    [ClearTimeSec] int NOT NULL,
    [LeaderCharIdx] int NOT NULL,
    [LeaderName] varchar(20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
    [LevelSum] int NOT NULL,
    [PartyMemberData] varbinary(300) NOT NULL,
    [CurrentRank] int NOT NULL,
    [LastRank] int NOT NULL,
    [ClearDate] datetime NULL,
    PRIMARY KEY ([RankingIdx])
);
        </pre>";
    }
} else {
    echo "<p class='error'>❌ ไม่สามารถตรวจสอบตารางได้</p>";
    if (sqlsrv_errors()) {
        echo "<pre class='error'>" . print_r(sqlsrv_errors(), true) . "</pre>";
    }
}

echo "</div>";

// สรุปผลการทดสอบ
echo "<div class='test-section'>
        <h3>📋 สรุปผลการทดสอบ</h3>";

$issues = [];

// ตรวจสอบปัญหาต่างๆ
if (!isset($conn) || $conn === false) {
    $issues[] = "ไม่สามารถเชื่อมต่อ Database ได้";
}

if (!isset($tableInfo) || $tableInfo['TableExists'] == 0) {
    $issues[] = "ไม่พบตาราง cabal_DungeonRankingParty_table";
}

if (empty($issues)) {
    echo "<p class='success'>🎉 <strong>ระบบพร้อมใช้งาน!</strong> ไม่พบปัญหาใดๆ</p>";
    echo "<p class='info'>✅ สามารถใช้งานระบบตรวจสอบ Dungeon Ranking Party ได้แล้ว</p>";
    echo "<p><a href='manage-dungeonranking.php' style='color: blue;'>👉 ไปยังหน้าจัดการ Dungeon Ranking</a></p>";
} else {
    echo "<p class='error'>❌ <strong>พบปัญหา:</strong></p>";
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li class='error'>$issue</li>";
    }
    echo "</ul>";
    echo "<p class='warning'>⚠️ กรุณาแก้ไขปัญหาข้างต้นก่อนใช้งาน</p>";
}

echo "</div>";

echo "</body></html>";
?>
