<?php
require('../../_app/dbinfo.inc.php');
require('../../_app/general_config.inc.php');

echo "<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>ทดสอบตาราง Dungeon Point</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        .test-section { 
            border: 1px solid #ddd; 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px; 
        }
        table { border-collapse: collapse; width: 100%; margin-top: 10px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        pre { background-color: #f5f5f5; padding: 10px; border-radius: 3px; }
    </style>
</head>
<body>";

echo "<h1>🧪 ทดสอบระบบ Dungeon Point</h1>";

// ทดสอบการเชื่อมต่อฐานข้อมูล
echo "<div class='test-section'>
        <h3>🔌 ทดสอบการเชื่อมต่อฐานข้อมูล</h3>";

$conn = db_connect();

if (isset($conn) && $conn !== false) {
    echo "<p class='success'>✅ เชื่อมต่อ Database สำเร็จ</p>";
    
    // ทดสอบ Query พื้นฐาน
    $testQuery = "SELECT @@VERSION AS SQLVersion, DB_NAME() AS DatabaseName";
    $testResult = sqlsrv_query($conn, $testQuery);
    
    if ($testResult) {
        $dbInfo = sqlsrv_fetch_array($testResult, SQLSRV_FETCH_ASSOC);
        echo "<p class='info'>📊 Database: " . htmlspecialchars($dbInfo['DatabaseName']) . "</p>";
        echo "<p class='info'>🔧 SQL Server Version: " . htmlspecialchars($dbInfo['SQLVersion']) . "</p>";
    }
} else {
    echo "<p class='error'>❌ ไม่สามารถเชื่อมต่อ Database ได้</p>";
    if (sqlsrv_errors()) {
        echo "<pre class='error'>" . print_r(sqlsrv_errors(), true) . "</pre>";
    }
}

echo "</div>";

// ทดสอบการมีอยู่ของตาราง cabal_DungeonPoint_table
echo "<div class='test-section'>
        <h3>📋 ตรวจสอบตาราง cabal_DungeonPoint_table</h3>";

$checkTableQuery = "
    SELECT COUNT(*) as TableExists 
    FROM INFORMATION_SCHEMA.TABLES 
    WHERE TABLE_NAME = 'cabal_DungeonPoint_table'
";

$checkTableResult = sqlsrv_query($conn, $checkTableQuery);

if ($checkTableResult) {
    $tableInfo = sqlsrv_fetch_array($checkTableResult, SQLSRV_FETCH_ASSOC);
    
    if ($tableInfo['TableExists'] > 0) {
        echo "<p class='success'>✅ ตาราง cabal_DungeonPoint_table มีอยู่ในระบบ</p>";
        
        // ตรวจสอบโครงสร้างตาราง
        $structureQuery = "
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'cabal_DungeonPoint_table'
            ORDER BY ORDINAL_POSITION
        ";
        
        $structureResult = sqlsrv_query($conn, $structureQuery);
        
        if ($structureResult) {
            echo "<h4>📊 โครงสร้างตาราง:</h4>";
            echo "<table>";
            echo "<tr><th>Column Name</th><th>Data Type</th><th>Nullable</th><th>Default</th></tr>";
            
            while ($column = sqlsrv_fetch_array($structureResult, SQLSRV_FETCH_ASSOC)) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($column['COLUMN_NAME']) . "</td>";
                echo "<td>" . htmlspecialchars($column['DATA_TYPE']) . "</td>";
                echo "<td>" . htmlspecialchars($column['IS_NULLABLE']) . "</td>";
                echo "<td>" . htmlspecialchars($column['COLUMN_DEFAULT'] ?? 'NULL') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // ตรวจสอบข้อมูลในตาราง
        echo "<h4>📈 สถิติข้อมูล:</h4>";
        
        // จำนวนแถวทั้งหมด
        $countQuery = "SELECT COUNT(*) as total_rows FROM [".DATABASE_SV."].[dbo].cabal_DungeonPoint_table";
        $countResult = sqlsrv_query($conn, $countQuery);
        if ($countResult) {
            $countData = sqlsrv_fetch_array($countResult, SQLSRV_FETCH_ASSOC);
            echo "<p class='info'>📊 จำนวนแถวทั้งหมด: " . number_format($countData['total_rows']) . " แถว</p>";
        }
        
        // จำนวนผู้เล่นที่มี DP > 0
        $activeDPQuery = "SELECT COUNT(*) as active_players FROM [".DATABASE_SV."].[dbo].cabal_DungeonPoint_table WHERE DP > 0";
        $activeDPResult = sqlsrv_query($conn, $activeDPQuery);
        if ($activeDPResult) {
            $activeDPData = sqlsrv_fetch_array($activeDPResult, SQLSRV_FETCH_ASSOC);
            echo "<p class='success'>✅ ผู้เล่นที่มี DP มากกว่า 0: " . number_format($activeDPData['active_players']) . " คน</p>";
        }
        
        // DP รวมทั้งหมด
        $totalDPQuery = "SELECT SUM(DP) as total_dp FROM [".DATABASE_SV."].[dbo].cabal_DungeonPoint_table";
        $totalDPResult = sqlsrv_query($conn, $totalDPQuery);
        if ($totalDPResult) {
            $totalDPData = sqlsrv_fetch_array($totalDPResult, SQLSRV_FETCH_ASSOC);
            echo "<p class='info'>💎 DP รวมทั้งหมดในระบบ: " . number_format($totalDPData['total_dp'] ?? 0) . " คะแนน</p>";
        }
        
        // ค่าเฉลี่ย DP
        $avgDPQuery = "SELECT AVG(CAST(DP as FLOAT)) as avg_dp FROM [".DATABASE_SV."].[dbo].cabal_DungeonPoint_table WHERE DP > 0";
        $avgDPResult = sqlsrv_query($conn, $avgDPQuery);
        if ($avgDPResult) {
            $avgDPData = sqlsrv_fetch_array($avgDPResult, SQLSRV_FETCH_ASSOC);
            echo "<p class='info'>📊 ค่าเฉลี่ย DP (ของผู้ที่มี DP > 0): " . number_format($avgDPData['avg_dp'] ?? 0, 2) . " คะแนน</p>";
        }
        
        // DP สูงสุด
        $maxDPQuery = "SELECT MAX(DP) as max_dp FROM [".DATABASE_SV."].[dbo].cabal_DungeonPoint_table";
        $maxDPResult = sqlsrv_query($conn, $maxDPQuery);
        if ($maxDPResult) {
            $maxDPData = sqlsrv_fetch_array($maxDPResult, SQLSRV_FETCH_ASSOC);
            echo "<p class='warning'>🏆 DP สูงสุด: " . number_format($maxDPData['max_dp'] ?? 0) . " คะแนน</p>";
        }
        
        // แสดงข้อมูลตัวอย่าง 5 แถวแรก
        echo "<h4>📋 ข้อมูลตัวอย่าง (5 แถวแรก):</h4>";
        $sampleQuery = "SELECT TOP 5 * FROM [".DATABASE_SV."].[dbo].cabal_DungeonPoint_table ORDER BY DP DESC";
        $sampleResult = sqlsrv_query($conn, $sampleQuery);
        
        if ($sampleResult) {
            echo "<table>";
            echo "<tr><th>CharacterIdx</th><th>DP</th><th>DPLastGetTime</th><th>วันที่ (แปลงแล้ว)</th></tr>";
            
            while ($sample = sqlsrv_fetch_array($sampleResult, SQLSRV_FETCH_ASSOC)) {
                $timeFormatted = 'ไม่เคยได้';
                if ($sample['DPLastGetTime'] && $sample['DPLastGetTime'] > 0) {
                    $timeFormatted = date('d/m/Y H:i:s', $sample['DPLastGetTime']);
                }
                
                echo "<tr>";
                echo "<td>" . htmlspecialchars($sample['CharacterIdx']) . "</td>";
                echo "<td>" . number_format($sample['DP']) . "</td>";
                echo "<td>" . htmlspecialchars($sample['DPLastGetTime']) . "</td>";
                echo "<td>" . htmlspecialchars($timeFormatted) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } else {
        echo "<p class='error'>❌ ไม่พบตาราง cabal_DungeonPoint_table</p>";
        echo "<p class='warning'>⚠️ กรุณาสร้างตารางด้วย SQL ต่อไปนี้:</p>";
        echo "<pre>
CREATE TABLE [".DATABASE_SV."].[dbo].[cabal_DungeonPoint_table] (
    [CharacterIdx] int NOT NULL,
    [DP] int DEFAULT 0 NOT NULL,
    [DPLastGetTime] int DEFAULT 0 NOT NULL,
    PRIMARY KEY ([CharacterIdx])
);
        </pre>";
    }
} else {
    echo "<p class='error'>❌ ไม่สามารถตรวจสอบตารางได้</p>";
    if (sqlsrv_errors()) {
        echo "<pre class='error'>" . print_r(sqlsrv_errors(), true) . "</pre>";
    }
}

echo "</div>";

// ทดสอบการเชื่อมโยงกับตาราง character
echo "<div class='test-section'>
        <h3>🔗 ทดสอบการเชื่อมโยงกับตาราง Character และ Auth</h3>";

echo "<p class='info'>💡 <strong>หมายเหตุ:</strong> การเชื่อมโยงกับตาราง cabal_auth_table ใช้สูตร <code>FLOOR(CharacterIdx/16) = UserNum</code></p>";
echo "<p class='info'>เนื่องจาก CharacterIdx = (UserNum × 16) + CharacterSlot</p>";

$joinTestQuery = "
    SELECT TOP 5
        dp.CharacterIdx,
        dp.DP,
        dp.DPLastGetTime,
        ct.Name,
        ct.Level,
        ct.Class,
        auth.UserNum,
        auth.ID as UserID
    FROM [".DATABASE_SV."].[dbo].cabal_DungeonPoint_table dp
    LEFT JOIN [".DATABASE_SV."].[dbo].cabal_character_table ct ON dp.CharacterIdx = ct.CharacterIdx
    LEFT JOIN [".DATABASE_ACC."].[dbo].cabal_auth_table auth ON FLOOR(ct.CharacterIdx/16) = auth.UserNum
    WHERE dp.DP > 0
    ORDER BY dp.DP DESC
";

$joinTestResult = sqlsrv_query($conn, $joinTestQuery);

if ($joinTestResult) {
    echo "<p class='success'>✅ การเชื่อมโยงกับตาราง Character สำเร็จ</p>";
    echo "<h4>📋 ข้อมูลตัวอย่างพร้อมชื่อตัวละครและ Username:</h4>";
    echo "<table>";
    echo "<tr><th>UserNum</th><th>Username</th><th>CharacterIdx</th><th>ชื่อตัวละคร</th><th>Level</th><th>Class</th><th>DP</th></tr>";

    while ($joinData = sqlsrv_fetch_array($joinTestResult, SQLSRV_FETCH_ASSOC)) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($joinData['UserNum'] ?? 'N/A') . "</td>";
        echo "<td>" . htmlspecialchars($joinData['UserID'] ?? 'N/A') . "</td>";
        echo "<td>" . htmlspecialchars($joinData['CharacterIdx']) . "</td>";
        echo "<td>" . htmlspecialchars($joinData['Name'] ?? 'N/A') . "</td>";
        echo "<td>" . htmlspecialchars($joinData['Level'] ?? 'N/A') . "</td>";
        echo "<td>" . htmlspecialchars($joinData['Class'] ?? 'N/A') . "</td>";
        echo "<td>" . number_format($joinData['DP']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p class='error'>❌ ไม่สามารถเชื่อมโยงกับตาราง Character ได้</p>";
    if (sqlsrv_errors()) {
        echo "<pre class='error'>" . print_r(sqlsrv_errors(), true) . "</pre>";
    }
}

echo "</div>";

// สรุปผลการทดสอบ
echo "<div class='test-section'>
        <h3>📋 สรุปผลการทดสอบ</h3>";

$issues = [];

// ตรวจสอบปัญหาต่างๆ
if (!isset($conn) || $conn === false) {
    $issues[] = "ไม่สามารถเชื่อมต่อ Database ได้";
}

if (!isset($tableInfo) || $tableInfo['TableExists'] == 0) {
    $issues[] = "ไม่พบตาราง cabal_DungeonPoint_table";
}

if (empty($issues)) {
    echo "<p class='success'>🎉 <strong>ระบบพร้อมใช้งาน!</strong> ไม่พบปัญหาใดๆ</p>";
    echo "<p class='info'>✅ สามารถใช้งานระบบตรวจสอบ Dungeon Point ได้แล้ว</p>";
    echo "<p><a href='manage-dungeonpoint.php' style='color: blue;'>👉 ไปยังหน้าจัดการ Dungeon Point</a></p>";
} else {
    echo "<p class='error'>❌ <strong>พบปัญหา:</strong></p>";
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li class='error'>$issue</li>";
    }
    echo "</ul>";
    echo "<p class='warning'>⚠️ กรุณาแก้ไขปัญหาข้างต้นก่อนใช้งาน</p>";
}

echo "</div>";

echo "</body></html>";
?>
